# Sistem Role Pengguna - Stock Manager

## Overview
Sistem ini telah diperbarui untuk mendukung tiga level role pengguna yang berbeda dengan hak akses yang sesuai.

## Role yang Tersedia

### 1. Pengguna (User)
- **Hak Akses**: <PERSON><PERSON><PERSON> dasar ke sistem
- **Permissions**: 
  - `is_staff = False`
  - `is_superuser = False`
- **Fitur yang dapat diakses**:
  - Dashboard
  - Melihat produk
  - Melakukan transaksi stok
  - Melihat laporan

### 2. Admin
- **Hak Akses**: Dapat mengelola pengguna dan memiliki akses ke semua fitur
- **Permissions**:
  - `is_staff = True`
  - `is_superuser = False`
- **Fitur yang dapat diakses**:
  - Semua fitur Pengguna
  - Mengelola pengguna (CRUD)
  - Akses ke pengaturan sistem
  - Mengelola kategori dan satuan

### 3. Owner
- **Hak Akses**: <PERSON><PERSON><PERSON> penuh ke semua fitur termasuk pengaturan sistem
- **Permissions**:
  - `is_staff = True`
  - `is_superuser = True`
- **Fitur yang dapat diakses**:
  - Semua fitur Admin
  - Akses penuh ke Django Admin
  - Dapat mengubah pengaturan sistem tingkat lanjut

## Implementasi

### Form Pengguna
- Ditambahkan field `role` dengan pilihan: Pengguna, Admin, Owner
- Form secara otomatis mengatur `is_staff` dan `is_superuser` berdasarkan role yang dipilih
- Saat edit pengguna, role saat ini terdeteksi otomatis

### Views
- Semua view user management sekarang menggunakan `@user_passes_test(is_admin_or_owner)`
- Hanya Admin dan Owner yang dapat mengakses manajemen pengguna

### Template
- Menu "Pengaturan" hanya muncul untuk Admin dan Owner
- Status role ditampilkan di header dan daftar pengguna
- Badge warna berbeda untuk setiap role:
  - Pengguna: Badge abu-abu
  - Admin: Badge biru
  - Owner: Badge merah

### Navigation
- Menu pengaturan hanya terlihat oleh Admin dan Owner
- Role pengguna ditampilkan di header sebelah username

## Cara Menggunakan

### Menambah Pengguna Baru
1. Login sebagai Admin atau Owner
2. Buka menu "Pengaturan" > "Tambah User"
3. Isi form dan pilih role yang sesuai
4. Klik "Simpan"

### Mengubah Role Pengguna
1. Login sebagai Admin atau Owner
2. Buka menu "Pengaturan" > "Kelola User"
3. Klik tombol edit pada pengguna yang ingin diubah
4. Ubah role di dropdown
5. Klik "Simpan"

## Keamanan
- Pengguna biasa tidak dapat mengakses manajemen pengguna
- Menu pengaturan tersembunyi dari pengguna biasa
- Permission check dilakukan di level view dan template

## Migration
Pengguna yang sudah ada akan otomatis terdeteksi rolenya:
- Jika `is_superuser = True` → Owner
- Jika `is_staff = True` → Admin  
- Jika keduanya `False` → Pengguna
