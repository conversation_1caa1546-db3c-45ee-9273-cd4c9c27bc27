<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}Stock Manager{% endblock %}</title>
    {% load static %}
    <!-- Google Fonts - Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap"
      rel="stylesheet"
    />
    <!-- CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    {% block head_extras %}{% endblock %}
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <div class="sidebar">
        <div class="navbar-brand">
          <i class="fas fa-box-open"></i>
          <span>Stock Manager</span>
        </div>
        <div class="sidebar-sticky">
          <ul class="nav">
            <li class="nav-item">
              <a
                href="{% url 'dashboard:index' %}"
                class="nav-link {% if request.path == '/dashboard/' %}active{% endif %}"
              >
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
              </a>
            </li>
            <li class="sidebar-heading">Produk</li>
            <li class="nav-item">
              <a
                href="{% url 'dashboard:product_list' %}"
                class="nav-link {% if '/products/' in request.path %}active{% endif %}"
              >
                <i class="fas fa-box"></i>
                <span>Produk</span>
              </a>
            </li>
            <li class="sidebar-heading">Stok</li>
            <li class="nav-item">
              <a
                href="{% url 'dashboard:stock_in' %}"
                class="nav-link {% if '/stock-in/' in request.path %}active{% endif %}"
              >
                <i class="fas fa-arrow-down"></i>
                <span>Stok Masuk</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                href="{% url 'dashboard:stock_out' %}"
                class="nav-link {% if '/stock-out/' in request.path %}active{% endif %}"
              >
                <i class="fas fa-arrow-up"></i>
                <span>Stok Keluar</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                href="{% url 'dashboard:stock_movement_list' %}"
                class="nav-link {% if '/stock-movement/' in request.path %}active{% endif %}"
              >
                <i class="fas fa-exchange-alt"></i>
                <span>Riwayat Stok</span>
              </a>
            </li>
            <li class="sidebar-heading">Laporan</li>
            <li class="nav-item">
              <a
                href="{% url 'dashboard:stock_report' %}"
                class="nav-link {% if '/reports/stock/' in request.path %}active{% endif %}"
              >
                <i class="fas fa-chart-bar"></i>
                <span>Laporan Stok</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                href="{% url 'dashboard:transaction_report' %}"
                class="nav-link {% if '/reports/transaction/' in request.path %}active{% endif %}"
              >
                <i class="fas fa-exchange-alt"></i>
                <span>Laporan Transaksi</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                href="{% url 'dashboard:financial_report' %}"
                class="nav-link {% if '/reports/financial/' in request.path %}active{% endif %}"
              >
                <i class="fas fa-money-bill-wave"></i>
                <span>Laporan Keuangan</span>
              </a>
            </li>
            {% if request.user.is_staff or request.user.is_superuser %}
            <li class="sidebar-heading">Pengaturan</li>
            <li class="nav-item">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="collapse"
                data-bs-target="#settingsSubmenu"
                aria-expanded="false"
              >
                <i class="fas fa-cog"></i>
                <span>Pengaturan</span>
                <i class="fas fa-chevron-down float-end"></i>
              </a>
              <ul class="collapse list-unstyled ms-4" id="settingsSubmenu">
                <li>
                  <a href="{% url 'dashboard:user_list' %}" class="nav-link"
                    >Kelola User</a
                  >
                </li>
                <li>
                  <a href="{% url 'dashboard:user_create' %}" class="nav-link"
                    >Tambah User</a
                  >
                </li>
              </ul>
            </li>
            {% endif %}
            <li class="nav-item">
              <a href="{% url 'dashboard:logout' %}" class="nav-link">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Content -->
      <div class="content">
        <!-- Header -->
        <div class="header">
          <div>
            <h1>{% block header_title %}Dashboard{% endblock %}</h1>
          </div>
          <div class="d-flex align-items-center">
            <button id="theme-toggle" class="btn btn-sm">
              <i class="fas fa-moon"></i>
            </button>
            <div class="ml-3">
              <span>{{ request.user.username }}</span>
              <small class="d-block text-muted">
                {% if request.user.is_superuser %} Owner {% elif
                request.user.is_staff %} Admin {% else %} Pengguna {% endif %}
              </small>
            </div>
            <!-- Notifikasi dropdown di navbar dihapus -->
          </div>
        </div>

        <!-- Main Content -->
        <main>
          {% if messages %}
          <div class="messages mb-4">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
            {% endfor %}
          </div>
          {% endif %} {% block content %}{% endblock %}
        </main>
      </div>
    </div>

    <!-- JavaScript -->
    <script src="{% static 'js/theme.js' %}"></script>
    <!-- Core JS -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>

    {% block scripts %}{% endblock %} {% block extra_js %}
    <script>
      // Hapus semua fungsi terkait dropdown notifikasi

      // Hanya tinggalkan fungsi lain yang diperlukan
      // Misalnya fungsi untuk toggle tema
      document
        .getElementById("theme-toggle")
        .addEventListener("click", function () {
          document.body.classList.toggle("dark-mode");

          // Toggle icon
          const icon = this.querySelector("i");
          if (icon.classList.contains("fa-moon")) {
            icon.classList.replace("fa-moon", "fa-sun");
          } else {
            icon.classList.replace("fa-sun", "fa-moon");
          }

          // Simpan preferensi tema ke localStorage
          const isDarkMode = document.body.classList.contains("dark-mode");
          localStorage.setItem("darkMode", isDarkMode);
        });
    </script>
    {% endblock %}
  </body>
</html>
